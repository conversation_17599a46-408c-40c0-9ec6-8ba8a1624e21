<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" href="dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'projects.php' ? 'active' : ''; ?>" href="projects.php">
                    <i class="fas fa-building"></i>
                    Projects
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'services.php' ? 'active' : ''; ?>" href="services.php">
                    <i class="fas fa-tools"></i>
                    Services
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'media.php' ? 'active' : ''; ?>" href="media.php">
                    <i class="fas fa-images"></i>
                    Media
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'messages.php' ? 'active' : ''; ?>" href="messages.php">
                    <i class="fas fa-envelope"></i>
                    Messages
                    <?php
                    try {
                        $database = new Database();
                        $conn = $database->getConnection();
                        $newMessagesCount = $conn->query("SELECT COUNT(*) FROM messages WHERE status = 'new'")->fetchColumn();
                        if ($newMessagesCount > 0) {
                            echo '<span class="badge bg-danger ms-2">' . $newMessagesCount . '</span>';
                        }
                    } catch (Exception $e) {
                        // Silently handle error
                    }
                    ?>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'pages.php' ? 'active' : ''; ?>" href="pages.php">
                    <i class="fas fa-file-alt"></i>
                    Pages
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>" href="users.php">
                    <i class="fas fa-users"></i>
                    Users
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </li>
        </ul>

        <h6 class="sidebar-heading">
            <span>Quick Actions</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="project-add.php">
                    <i class="fas fa-plus"></i>
                    Add Project
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="service-add.php">
                    <i class="fas fa-plus"></i>
                    Add Service
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="media-upload.php">
                    <i class="fas fa-upload"></i>
                    Upload Media
                </a>
            </li>
        </ul>

        <h6 class="sidebar-heading">
            <span>Website</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="../index.php" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    View Website
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="backup.php">
                    <i class="fas fa-download"></i>
                    Backup Data
                </a>
            </li>
        </ul>
    </div>
</nav>
