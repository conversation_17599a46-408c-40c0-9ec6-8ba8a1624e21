    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- Performance Optimized JS Loading -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
    <!-- DataTables JS (optimized loading) -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js" crossorigin="anonymous"></script>
    <!-- Performance Optimization Script -->
    <script src="assets/js/performance.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // Initialize DataTables
        $(document).ready(function() {
            $('.data-table').DataTable({
                "pageLength": 25,
                "responsive": true,
                "order": [[ 0, "desc" ]],
                "language": {
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });
        });

        // Confirm delete actions
        function confirmDelete(message = 'Are you sure you want to delete this item?') {
            return confirm(message);
        }

        // Show loading spinner
        function showLoading() {
            const loadingHtml = `
                <div class="d-flex justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;
            return loadingHtml;
        }

        // Show success message
        function showSuccess(message) {
            const alertHtml = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.container-fluid').prepend(alertHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }

        // Show error message
        function showError(message) {
            const alertHtml = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.container-fluid').prepend(alertHtml);
        }

        // AJAX form submission
        function submitForm(formId, successCallback) {
            const form = document.getElementById(formId);
            const formData = new FormData(form);
            
            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    if (successCallback) {
                        successCallback(data);
                    }
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('An error occurred. Please try again.');
            });
        }

        // File upload preview
        function previewImage(input, previewId) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById(previewId).src = e.target.result;
                    document.getElementById(previewId).style.display = 'block';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Auto-save functionality
        function autoSave(formId, interval = 30000) {
            setInterval(function() {
                const form = document.getElementById(formId);
                if (form) {
                    const formData = new FormData(form);
                    formData.append('auto_save', '1');
                    
                    fetch(form.action, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show subtle indication of auto-save
                            const saveIndicator = document.getElementById('save-indicator');
                            if (saveIndicator) {
                                saveIndicator.textContent = 'Auto-saved at ' + new Date().toLocaleTimeString();
                                saveIndicator.style.display = 'block';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Auto-save error:', error);
                    });
                }
            }, interval);
        }

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });

        // Initialize dropdowns
        var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });

        // Sidebar toggle for mobile
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.querySelector('.navbar-toggler');
            const sidebar = document.getElementById('sidebarMenu');
            
            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }
        });

        // Enhanced Real-time notifications and user display
        function initializeNotifications() {
            // Check for new messages and update badge
            function checkNotifications() {
                fetch('../api/notifications.php')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateMessageBadge(data.data.new_messages || 0);
                            updateNotificationBadge(data.data.total_notifications || 0);
                        }
                    })
                    .catch(error => {
                        console.error('Notification error:', error);
                    });
            }

            // Load user statistics for quick stats
            function loadUserStats() {
                showStatsLoading();

                fetch('../api/user-stats.php')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            updateQuickStats(data);
                        } else {
                            throw new Error(data.message || 'Failed to load stats');
                        }
                    })
                    .catch(error => {
                        console.error('User stats error:', error);
                        showStatsError();

                        // Set fallback values after a delay
                        setTimeout(() => {
                            updateQuickStats({
                                total_projects: 0,
                                total_messages: 0
                            });
                        }, 2000);
                    });
            }

            // Initial checks
            checkNotifications();
            loadUserStats();

            // Check notifications every minute
            setInterval(checkNotifications, 60000);

            // Update stats every 5 minutes
            setInterval(loadUserStats, 300000);
        }

        function updateMessageBadge(count) {
            const badge = document.getElementById('message-badge');
            if (badge) {
                badge.textContent = count;
                badge.style.display = count > 0 ? 'inline-flex' : 'none';

                // Add animation for new messages
                if (count > 0) {
                    badge.classList.add('animate__animated', 'animate__pulse');
                    setTimeout(() => {
                        badge.classList.remove('animate__animated', 'animate__pulse');
                    }, 1000);
                }
            }
        }

        function updateNotificationBadge(count) {
            const badge = document.getElementById('notification-badge');
            if (badge) {
                badge.textContent = count;
                badge.style.display = count > 0 ? 'inline-flex' : 'none';

                // Add animation for new notifications
                if (count > 0) {
                    badge.classList.add('animate__animated', 'animate__bounce');
                    setTimeout(() => {
                        badge.classList.remove('animate__animated', 'animate__bounce');
                    }, 1000);
                }
            }
        }

        function updateQuickStats(data) {
            // Update projects count
            const projectsCount = document.getElementById('user-projects-count');
            if (projectsCount) {
                const count = data.total_projects || 0;
                animateNumber(projectsCount, parseInt(projectsCount.textContent) || 0, count);

                // Add detailed tooltip
                const projectsItem = projectsCount.closest('.quick-stat-item');
                if (projectsItem && data.stats_breakdown) {
                    const breakdown = data.stats_breakdown.projects;
                    projectsItem.title = `Total: ${breakdown.total}\nActive: ${breakdown.active}\nCompleted: ${breakdown.completed}\nRecent (30 days): ${breakdown.recent}`;
                }
            }

            // Update messages count
            const messagesCount = document.getElementById('user-messages-count');
            if (messagesCount) {
                const count = data.total_messages || 0;
                animateNumber(messagesCount, parseInt(messagesCount.textContent) || 0, count);

                // Add detailed tooltip
                const messagesItem = messagesCount.closest('.quick-stat-item');
                if (messagesItem && data.stats_breakdown) {
                    const breakdown = data.stats_breakdown.messages;
                    messagesItem.title = `Total: ${breakdown.total}\nUnread: ${breakdown.unread}\nNew Today: ${breakdown.new_today}`;
                }
            }

            // Update growth indicators if available
            if (data.projects_growth !== undefined) {
                updateGrowthIndicator('projects-growth', data.projects_growth);
            }

            if (data.messages_growth !== undefined) {
                updateGrowthIndicator('messages-growth', data.messages_growth);
            }

            // Add fade-in animation to stats
            const statsContainer = document.querySelector('.dropdown-quick-stats');
            if (statsContainer) {
                statsContainer.classList.add('animate__animated', 'animate__fadeInUp');
                setTimeout(() => {
                    statsContainer.classList.remove('animate__animated', 'animate__fadeInUp');
                }, 1000);
            }

            // Log performance metrics if available
            if (data.performance) {
                console.log('Stats loaded in:', data.performance.response_time, 'Memory usage:', data.performance.memory_usage);
            }
        }

        function animateNumber(element, start, end, duration = 1000) {
            if (start === end) {
                element.textContent = end;
                return;
            }

            const range = end - start;
            const increment = range / (duration / 16); // 60fps
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if ((increment > 0 && current >= end) || (increment < 0 && current <= end)) {
                    current = end;
                    clearInterval(timer);
                }
                element.textContent = Math.round(current);
            }, 16);
        }

        function updateGrowthIndicator(elementId, growth) {
            const indicator = document.getElementById(elementId);
            if (indicator && growth !== undefined && growth !== null) {
                const isPositive = growth >= 0;
                const isZero = growth === 0;

                if (isZero) {
                    indicator.style.display = 'none';
                } else {
                    indicator.style.display = 'inline-block';
                    indicator.textContent = `${isPositive ? '+' : ''}${growth}%`;
                    indicator.className = `stat-growth ${isPositive ? 'positive' : 'negative'}`;

                    // Add animation
                    indicator.style.opacity = '0';
                    indicator.style.transform = 'scale(0.8)';

                    setTimeout(() => {
                        indicator.style.transition = 'all 0.3s ease-in-out';
                        indicator.style.opacity = '1';
                        indicator.style.transform = 'scale(1)';
                    }, 100);
                }
            }
        }

        // Add click handlers for quick stats
        function initializeQuickStatsHandlers() {
            // Handle stat item clicks using data-target attribute
            const statItems = document.querySelectorAll('.quick-stat-item[data-target]');
            statItems.forEach(item => {
                item.style.cursor = 'pointer';

                item.addEventListener('click', function() {
                    const target = this.getAttribute('data-target');
                    if (target) {
                        window.location.href = target;
                    }
                });

                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.02)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Handle refresh button
            const refreshBtn = document.getElementById('refreshStats');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function(e) {
                    e.stopPropagation(); // Prevent dropdown from closing
                    refreshQuickStats();
                });
            }
        }

        // Refresh quick stats function
        function refreshQuickStats() {
            const refreshBtn = document.getElementById('refreshStats');
            if (refreshBtn) {
                refreshBtn.classList.add('loading');
                refreshBtn.disabled = true;
            }

            showStatsLoading();

            fetch('../api/user-stats.php?refresh=' + Date.now()) // Add cache buster
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        updateQuickStats(data);
                        showRefreshSuccess();
                    } else {
                        throw new Error(data.message || 'Failed to refresh stats');
                    }
                })
                .catch(error => {
                    console.error('Refresh stats error:', error);
                    showStatsError();
                })
                .finally(() => {
                    if (refreshBtn) {
                        refreshBtn.classList.remove('loading');
                        refreshBtn.disabled = false;
                    }
                });
        }

        // Show refresh success feedback
        function showRefreshSuccess() {
            const refreshBtn = document.getElementById('refreshStats');
            if (refreshBtn) {
                const originalIcon = refreshBtn.innerHTML;
                refreshBtn.innerHTML = '<i class="fas fa-check"></i>';
                refreshBtn.style.background = 'var(--success-color)';
                refreshBtn.style.color = 'white';

                setTimeout(() => {
                    refreshBtn.innerHTML = originalIcon;
                    refreshBtn.style.background = '';
                    refreshBtn.style.color = '';
                }, 1500);
            }
        }

        // Add loading states for stats
        function showStatsLoading() {
            const projectsCount = document.getElementById('user-projects-count');
            const messagesCount = document.getElementById('user-messages-count');

            if (projectsCount) {
                projectsCount.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            }

            if (messagesCount) {
                messagesCount.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            }
        }

        // Add error handling for stats
        function showStatsError() {
            const projectsCount = document.getElementById('user-projects-count');
            const messagesCount = document.getElementById('user-messages-count');

            if (projectsCount) {
                projectsCount.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i>';
                projectsCount.title = 'Failed to load projects count';
            }

            if (messagesCount) {
                messagesCount.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i>';
                messagesCount.title = 'Failed to load messages count';
            }
        }

        // User dropdown enhancements
        function initializeUserDropdown() {
            const userDropdown = document.getElementById('userDropdown');
            const dropdownMenu = document.querySelector('.user-dropdown-menu');

            if (userDropdown && dropdownMenu) {
                // Add smooth animation
                userDropdown.addEventListener('show.bs.dropdown', function () {
                    dropdownMenu.style.opacity = '0';
                    dropdownMenu.style.transform = 'translateY(-10px)';

                    setTimeout(() => {
                        dropdownMenu.style.transition = 'all 0.3s ease-in-out';
                        dropdownMenu.style.opacity = '1';
                        dropdownMenu.style.transform = 'translateY(0)';
                    }, 10);
                });

                userDropdown.addEventListener('hide.bs.dropdown', function () {
                    dropdownMenu.style.opacity = '0';
                    dropdownMenu.style.transform = 'translateY(-10px)';
                });
            }
        }

        // Update last seen time
        function updateLastSeen() {
            fetch('../api/update-last-seen.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    timestamp: new Date().toISOString()
                })
            }).catch(error => {
                console.error('Last seen update error:', error);
            });
        }

        // Initialize all enhancements
        document.addEventListener('DOMContentLoaded', function() {
            initializeNotifications();
            initializeUserDropdown();
            initializeQuickStatsHandlers();

            // Update last seen every 5 minutes
            setInterval(updateLastSeen, 300000);

            // Update on page visibility change
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    updateLastSeen();
                }
            });

            // Add keyboard shortcut for global search
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    const searchInput = document.getElementById('globalSearch');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }
            });

            // Add search functionality
            const searchInput = document.getElementById('globalSearch');
            if (searchInput) {
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        performGlobalSearch(this.value);
                    }, 300);
                });
            }
        });

        // Global search function
        function performGlobalSearch(query) {
            if (query.length < 2) return;

            // This could be expanded to search across projects, messages, etc.
            console.log('Searching for:', query);

            // Example: redirect to projects page with search query
            if (query.toLowerCase().includes('project')) {
                // Could implement live search results here
            }
        }
    </script>
</body>
</html>
