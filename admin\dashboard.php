<?php
require_once __DIR__ . '/../includes/functions.php';
requireLogin();

// Get dashboard statistics
$database = new Database();
$conn = $database->getConnection();

$stats = [];
$stats['total_projects'] = $conn->query("SELECT COUNT(*) FROM projects")->fetchColumn();
$stats['completed_projects'] = $conn->query("SELECT COUNT(*) FROM projects WHERE status = 'completed'")->fetchColumn();
$stats['ongoing_projects'] = $conn->query("SELECT COUNT(*) FROM projects WHERE status = 'ongoing'")->fetchColumn();
$stats['total_services'] = $conn->query("SELECT COUNT(*) FROM services WHERE status = 'active'")->fetchColumn();
$stats['total_media'] = $conn->query("SELECT COUNT(*) FROM media")->fetchColumn();
$stats['new_messages'] = $conn->query("SELECT COUNT(*) FROM messages WHERE status = 'new'")->fetchColumn();

// Recent activities
$recentProjects = getProjects(null, 5);
$recentMessages = getMessages('new');
$recentMessages = array_slice($recentMessages, 0, 5);

include 'includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="page-header d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center">
                <div>
                    <h1>Dashboard</h1>
                    <p class="text-muted mb-0">Welcome back! Here's what's happening with your projects.</p>
                </div>
                <div class="btn-toolbar">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card border-left-primary h-100">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs text-primary mb-2">
                                    Total Projects
                                </div>
                                <div class="h5 text-dark">
                                    <?php echo $stats['total_projects']; ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-building fa-2x" style="color: var(--primary-color); opacity: 0.7;"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card border-left-success h-100">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs text-success mb-2">
                                    Completed Projects
                                </div>
                                <div class="h5 text-dark">
                                    <?php echo $stats['completed_projects']; ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x" style="color: var(--success-color); opacity: 0.7;"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card border-left-info h-100">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs text-info mb-2">
                                    Active Services
                                </div>
                                <div class="h5 text-dark">
                                    <?php echo $stats['total_services']; ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-tools fa-2x" style="color: var(--info-color); opacity: 0.7;"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card border-left-warning h-100">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs text-warning mb-2">
                                    New Messages
                                </div>
                                <div class="h5 text-dark">
                                    <?php echo $stats['new_messages']; ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-envelope fa-2x" style="color: var(--warning-color); opacity: 0.7;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Projects and Messages -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Projects</h6>
                            <a href="projects.php" class="btn btn-sm btn-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Project</th>
                                            <th>Location</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentProjects as $project): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($project['title']); ?></td>
                                            <td><?php echo htmlspecialchars($project['location']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $project['status'] == 'completed' ? 'success' : ($project['status'] == 'ongoing' ? 'warning' : 'info'); ?>">
                                                    <?php echo ucfirst($project['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($project['created_at'])); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">New Messages</h6>
                            <a href="messages.php" class="btn btn-sm btn-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recentMessages)): ?>
                                <p class="text-muted">No new messages</p>
                            <?php else: ?>
                                <?php foreach ($recentMessages as $message): ?>
                                <div class="message-item border-bottom pb-2 mb-2">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($message['name']); ?></h6>
                                    <p class="mb-1 small text-muted"><?php echo htmlspecialchars($message['email']); ?></p>
                                    <p class="mb-1 small"><?php echo htmlspecialchars(substr($message['message'], 0, 100)) . '...'; ?></p>
                                    <small class="text-muted"><?php echo date('M d, Y H:i', strtotime($message['created_at'])); ?></small>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/admin_footer.php'; ?>
