import { NotificationAction } from './Notifications.types';
declare const _default: {
    getNotificationCategoriesAsync(): Promise<null>;
    setNotificationCategoryAsync(identifier: string, actions: NotificationAction[], options?: object): Promise<null>;
    deleteNotificationCategoryAsync(identifier: string): Promise<null>;
};
export default _default;
//# sourceMappingURL=NotificationCategoriesModule.web.d.ts.map